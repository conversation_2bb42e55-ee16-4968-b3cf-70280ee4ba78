package dev.riza.cuan.config;

import dev.riza.cuan.core.model.AdaptiveBar;
import dev.riza.cuan.core.model.MarketConditionEvent;
import dev.riza.cuan.domain.model.AggTradeEvent;
import dev.riza.cuan.domain.model.OrderBookSnapshot;
import org.springframework.integration.annotation.Gateway;
import org.springframework.integration.annotation.MessagingGateway;

@MessagingGateway
public interface MarketDataGateway {

    @Gateway(requestChannel = "depthDataChannel")
    void publish(OrderBookSnapshot event);

    @Gateway(requestChannel = "aggTradeChannel")
    void publish(AggTradeEvent trade);

    @Gateway(requestChannel = "marketConditionChannel")
    void publish(MarketConditionEvent trade);

    @Gateway(requestChannel = "adaptiveBarCannel")
    void publish(AdaptiveBar trade);
}
